{"name": "hmgrimoire", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "start": "turbo run start", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "start:backend": "turbo run dev --filter=backend", "start:frontend": "turbo run dev --filter=frontend", "stop": "pkill -f \"tsx watch\" && pkill -f \"vite\""}, "devDependencies": {"copyfiles": "^2.4.1", "prettier": "^3.6.2", "turbo": "^2.5.5", "typescript": "5.8.3"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}