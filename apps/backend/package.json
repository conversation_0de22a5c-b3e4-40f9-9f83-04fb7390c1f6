{"name": "backend", "version": "1.0.0", "description": "HarnMaster Grimoire API", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "prisma generate && tsc && copyfiles -u 1 \"src/generated/**/*\" dist\n", "start": "node dist/server.js", "check-types": "tsc --noEmit"}, "keywords": ["harnmaster", "spells", "ttrpg"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.12.0", "@repo/types": "workspace:*", "@repo/validation": "workspace:*", "cors": "^2.8.5", "express": "^5.1.0", "prisma": "^6.12.0", "sqlite3": "^5.1.7", "tsx": "^4.20.3", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^22.15.3", "@types/sqlite3": "^3.1.11", "typescript": "^5.8.3"}}